<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="人员外出培训审批表" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center" colspan="4">年度监督计划表</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th width="30%">监察人员</th>
            <th width="40%">授权检测项目</th>
            <th width="30%">监督频率</th>
          </tr>
          <tr v-for='(x, i) in formData.sonDOS' :key='x.id || i'>
            <td>{{ x.userName || '' }}</td>
            <td>{{ x.authorizationTestingProject || '' }}</td>
            <td>{{ x.supervisionFrequency || '' }}</td>
          </tr>
          <tr style="height: 6rem;text-align: left;">
            <td colspan="3" style="text-align: left;">
              监督重点分析:{{ formData.keyAnalysis }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">监督员:{{ formData.commitPerson_dictText }}</a-col>
                <a-col :span="12">日期:{{ formData.commitTime }}</a-col>
              </a-row>
            </td>
          </tr>
          <tr style="height: 6rem;text-align: left;">
            <td colspan="3" style="text-align: left;">
              审批意见:{{ formData.auditContent }}
              <br>
              <br>
              <br>
              <a-row>
                <a-col :span="12">技术负责人:{{ formData.auditPerson_dictText }}</a-col>
                <a-col :span="12">日期:{{ formData.auditTime }}</a-col>
              </a-row>
            </td>
          </tr>

        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, computed, unref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

// 定义表单数据接口
interface FormDataType {
  sonDOS: Array<{
    id?: string;
    userName?: string;
    authorizationTestingProject?: string;
    supervisionFrequency?: string;
  }>;
  keyAnalysis: string;
  commitPerson_dictText: string;
  commitTime: string;
  auditContent: string;
  auditPerson_dictText: string;
  auditTime: string;
}

// 定义表单数据
const formData = reactive<FormDataType>({
  // 子表数据 - 监察人员信息
  sonDOS: [],
  // 监督重点分析
  keyAnalysis: '',
  // 监督员信息
  commitPerson_dictText: '',
  commitTime: '',
  // 审批信息
  auditContent: '',
  auditPerson_dictText: '',
  auditTime: '',
});

// 重置表单数据
const resetFormData = () => {
  formData.sonDOS = [];
  formData.keyAnalysis = '';
  formData.commitPerson_dictText = '';
  formData.commitTime = '';
  formData.auditContent = '';
  formData.auditPerson_dictText = '';
  formData.auditTime = '';
};

//表单赋值
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  // 先重置表单数据
  resetFormData();

  if (data && data.record) {
    // 如果有传入数据，则赋值
    // 子表数据赋值
    formData.sonDOS = Array.isArray(data.record.sonDOS) ? data.record.sonDOS : [];

    // 监督重点分析
    formData.keyAnalysis = data.record.keyAnalysis || '';

    // 监督员信息
    formData.commitPerson_dictText = data.record.commitPerson_dictText || '';
    formData.commitTime = data.record.commitTime || '';

    // 审批信息
    formData.auditContent = data.record.auditContent || '';
    formData.auditPerson_dictText = data.record.auditPerson_dictText || '';
    formData.auditTime = data.record.auditTime || '';
  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
