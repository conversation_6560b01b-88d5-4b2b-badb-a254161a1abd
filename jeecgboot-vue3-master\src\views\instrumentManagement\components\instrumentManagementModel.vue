<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit" :width="1400">


    <a-tabs v-model:activeKey="activeKey" type="card" size="large">
      <a-tab-pane key="basic" tab="基本信息">
        <BasicForm @register="registerForm">
          <!-- <template #add="{ field }">
            {{field}}
            <a-cascader v-model:value="storagePlace" :options="cOption" placeholder="Please select" />
          </template> -->

        </BasicForm>
      </a-tab-pane>
      <a-tab-pane key="batch" tab="校准记录">
        <a-table class="j-table-force-nowrap" :columns="tableColumns" :data-source="batchNoList" :pagination="10" :scroll="{ x: true }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <span>
                <!-- <a @click="toAction(record, 'update')">填写COA</a>
                <a-divider type="vertical" /> -->
                <a @click="toAction(record, 'detail')">详情</a>
              </span>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
      

       <a-tab-pane key="batch" tab="采购记录">
        <a-table class="j-table-force-nowrap" :columns="procurementTableColumns" :data-source="procurementTableData" :pagination="10" :scroll="{ x: true }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <span>
                <!-- <a @click="toAction(record, 'update')">填写COA</a>
                <a-divider type="vertical" /> -->
                <a @click="toAction(record, 'detail')">详情</a>
              </span>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
    
    <CalibrationDetailModal @register="cdReg" @success="handleSuccess"></CalibrationDetailModal>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref, inject, reactive } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { customerFormSchema } from '../instrumentManagement.data';
  import { saveForm, manList } from '../instrumentManagement.api';
  import CalibrationDetailModal from './CalibrationDetailModal.vue'
import { defHttp } from '/@/utils/http/axios';
  //接收主表id
  const orderId = inject('orderId') || '';
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  const cOption = ref([]);
  let activeKey=ref('basic')
  const batchNoList = reactive([]);
  const tableColumns = [
    {
      title: '鉴定日期',
      dataIndex: 'calibrationDate',
      key: 'calibrationDate',
    },
    {
      title: '证书编号',
      dataIndex: 'certificateNo',
      key: 'certificateNo',
    },
    {
      title: '确认人',
      dataIndex: 'confirmedBy',
      key: 'confirmedBy',
    },
    {
      title: '下次校准日期',
      dataIndex: 'nextCalibrationDate',
      key: 'nextCalibrationDate',
    },
    {
      title: '操作',
      key: 'action',
    }
  ]
  const procurementTableData = ref([]);
  const procurementTableColumns = [
    {
      title: '鉴定日期',
      dataIndex: 'calibrationDate',
      key: 'calibrationDate',
    },
    {
      title: '操作',
      key: 'action',
    }
  ]
  const [cdReg, { openModal: openDetailModel }] = useModal();
  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, getFieldsValue }] = useForm({
    labelWidth: 150,
    schemas: customerFormSchema,
    showActionButtonGroup: false,
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    let placeList = [];
    batchNoList.length = 0;
    activeKey.value='basic'
    console.log("🚀 ~ file: instrumentManagementModel.vue:37 ~ getPlaceTree ~ res:", data.res)
    for(let i = 0; i < data.res.length; i++) {
      placeList.push(data.res[i])
    }
    // getPlaceTree().then((res)=> {
      
    //   for(let i = 0; i < res.length; i++) {
    //     placeList.push(res[i])
        
        
    //   }
    //   console.log("🚀 ~ file: instrumentManagementModel.vue:41 ~ getPlaceTree ~ placeList:", placeList)
    // })
    
    
    await updateSchema([
      {
        label: '仪器编号',
        field: 'instrumentNo',
        component: 'Input',
        required: true,
        componentProps: {
          disabled: false,
        },
        colProps: { span: 8 },
      },
      {
        label: '上次校准日期',
        field: 'lastCalibrationDate',
        component: 'DatePicker',
        required: true,
        componentProps: {
          valueFormat: 'YYYY-MM-DD',
          disabled: false,
        },
        colProps: { span: 8 },
        rules: [
          {
            required: true,
            // @ts-ignore
            validator: async (rule, value) => {
              if (!value) {
                /* eslint-disable-next-line */
                return Promise.reject('值不能为空');
              }
              if (isFuture(value)) {
                /* eslint-disable-next-line */
                return Promise.reject('不能选择未来的日期');
              }
              return Promise.resolve();
            },
            trigger: 'change',
          },
        ],
      },
      {
        label: '仪器状态',
        field: 'status',
        component: 'Select',
        required: true,
        defaultValue: '1',
        componentProps: {
          disabled: false,
          options: [
            {
              label: '正常使用',
              value: '1',
              key: '2',
            },
            {
              label: '校准',
              value: '2',
              key: '3',
            },
            {
              label: '维修',
              value: '3',
              key: '4',
            },
            {
              label: '报废中',
              value: '4',
              key: '5',
            },
            {
              label: '已报废',
              value: '5',
              key: '6',
            },
            {
              label: '已删除',
              value: '6',
              key: '7',
            },
          ],
        },
        colProps: {
          span: 8,
        },
      },
      {
        label: '存放地',
        field: 'storagePlace',
        required: true,
        component: 'Cascader',
        componentProps: {
          options: placeList
        },
        colProps: {
          span: 8,
        },
      },
    ]);
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      //表单赋值
      await updateSchema([
        {
          label: '仪器编号',
          field: 'instrumentNo',
          component: 'Input',
          required: true,
          componentProps: {
            disabled: true,
          },
          colProps: { span: 8 },
        },
        {
          label: '上次校准日期',
          field: 'lastCalibrationDate',
          component: 'DatePicker',
          required: true,
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
            disabled: true,
          },
          colProps: { span: 8 },
          rules: [
            {
              required: true,
              // @ts-ignore
              validator: async (rule, value) => {
                if (!value) {
                  /* eslint-disable-next-line */
                  return Promise.reject('值不能为空');
                }
                if (isFuture(value)) {
                  /* eslint-disable-next-line */
                  return Promise.reject('不能选择未来的日期');
                }
                return Promise.resolve();
              },
              trigger: 'change',
            },
          ],
        },
        {
          label: '仪器状态',
          field: 'status',
          component: 'Select',
          required: true,
          defaultValue: '1',
          componentProps: {
            disabled: true,
            options: [
              {
                label: '正常使用',
                value: '1',
                key: '2',
              },
              {
                label: '校准',
                value: '2',
                key: '3',
              },
              {
                label: '维修',
                value: '3',
                key: '4',
              },
              {
                label: '报废中',
                value: '4',
                key: '5',
              },
              {
                label: '已报废',
                value: '5',
                key: '6',
              },
              {
                label: '已删除',
                value: '6',
                key: '7',
              },
            ],
          },
          colProps: {
            span: 8,
          },
        },
      ]);
      await setFieldsValue({
        ...data.record,
      });

      manList({ instrumentNo: data.record.instrumentNo }).then((res) => {
        for (let i = 0; i < res.length; i++) {
          // if(res.records[i].createTime) {
          //     res.records[i].createTime = dayjs(res.records[i].createTime).format('YYYY-MM-DD');
          // }
          batchNoList.push(res[i])
        }
      })
      procurementTableData.value = []
      defHttp.get({ url: '', params: { reagentNo: data.record.reagentNo } }).then((res) => {
        procurementTableData.value = res
      });
    }
  });
  //设置标题
  const getTitle = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));

  //表单提交事件
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      if (unref(orderId)) {
        values.orderId = unref(orderId);
      }
      //提交表单
      await saveForm(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  function isFuture(str) {
    let d = new Date(str.replace(/-/g, '/'));
    let todaysDate = new Date();
    if (d.setHours(0, 0, 0, 0) > todaysDate.setHours(0, 0, 0, 0)) {
      return true;
    } else {
      return false;
    }
  }
  function toAction(record, type) {
    if(type == 'update') {
        openCoaModel(true, {
            record: { parentId: record.parentId, batchNo: record.batchNo },
            isUpdate: true,
            showFooter: true,
        })
    } else if(type == 'detail') {
      let param = {
          ...getFieldsValue(),
          ...record
      }
      openDetailModel(true, {
          record: param,
          isUpdate: true,
          showFooter: false,
      })
    }
}
</script>
